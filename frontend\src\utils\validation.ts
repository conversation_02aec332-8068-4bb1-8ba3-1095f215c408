/**
 * Validation utility functions for form inputs
 */

export type ValidationResult = string | null;
export type ValidatorFunction = (value: string, ...args: any[]) => ValidationResult;

export interface ValidationRule {
  validate: ValidatorFunction;
  message?: string;
}

/**
 * Validates email address format
 */
export const validateEmail = (
  email: string,
  customMessage?: string
): ValidationResult => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(email)) {
    return customMessage || 'Please enter a valid email address';
  }

  return null;
};

/**
 * Validates password length
 */
export const validatePassword = (
  password: string,
  customMessage?: string,
  minLength: number = 6
): ValidationResult => {
  if (password.length < minLength) {
    return customMessage || `Password must be at least ${minLength} characters long`;
  }
  
  return null;
};

/**
 * Validates that a field is not empty
 */
export const validateRequired = (
  value: string,
  customMessage?: string,
  fieldName?: string
): ValidationResult => {
  if (!value.trim()) {
    if (customMessage) {
      return customMessage;
    }
    if (fieldName) {
      return `${fieldName} is required`;
    }
    return 'This field is required';
  }
  
  return null;
};

/**
 * Validates that two passwords match
 */
export const validatePasswordMatch = (
  password: string,
  confirmPassword: string,
  customMessage?: string
): ValidationResult => {
  if (password !== confirmPassword) {
    return customMessage || 'Passwords do not match';
  }
  
  return null;
};

/**
 * Validates name format (letters, spaces, hyphens, apostrophes)
 */
export const validateName = (
  name: string,
  customMessage?: string
): ValidationResult => {
  const nameRegex = /^[a-zA-ZÀ-ÿ\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF\u4e00-\u9fff\s'-]+$/;
  
  if (!name.trim() || !nameRegex.test(name)) {
    return customMessage || 'Please enter a valid name (letters, spaces, hyphens, and apostrophes only)';
  }
  
  return null;
};

/**
 * Validates phone number format
 */
export const validatePhoneNumber = (
  phone: string,
  customMessage?: string
): ValidationResult => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  
  if (!phone.trim() || !phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
    return customMessage || 'Please enter a valid phone number';
  }
  
  return null;
};

/**
 * Validates URL format
 */
export const validateUrl = (
  url: string,
  customMessage?: string
): ValidationResult => {
  try {
    new URL(url);
    return null;
  } catch {
    return customMessage || 'Please enter a valid URL';
  }
};

/**
 * Validates that a value is within a specific length range
 */
export const validateLength = (
  value: string,
  minLength: number,
  maxLength: number,
  customMessage?: string
): ValidationResult => {
  if (value.length < minLength || value.length > maxLength) {
    return customMessage || `Must be between ${minLength} and ${maxLength} characters`;
  }
  
  return null;
};

/**
 * Validates that a value matches a specific pattern
 */
export const validatePattern = (
  value: string,
  pattern: RegExp,
  customMessage?: string
): ValidationResult => {
  if (!pattern.test(value)) {
    return customMessage || 'Invalid format';
  }
  
  return null;
};

/**
 * Creates a composite validator that applies multiple validation rules
 */
export const createValidator = (rules: ValidationRule[]) => {
  return (value: string): ValidationResult => {
    for (const rule of rules) {
      const result = rule.validate(value);
      if (result !== null) {
        return rule.message || result;
      }
    }
    return null;
  };
};

/**
 * Common validation rule sets for different form fields
 */
export const validationRules = {
  email: [
    { validate: validateRequired, message: 'Email is required' },
    { validate: validateEmail, message: 'Please enter a valid email address' },
  ],
  
  password: [
    { validate: validateRequired, message: 'Password is required' },
    { validate: validatePassword, message: 'Password must be at least 6 characters long' },
  ],
  
  name: [
    { validate: validateRequired, message: 'Name is required' },
    { validate: validateName, message: 'Please enter a valid name' },
  ],
  
  phone: [
    { validate: validateRequired, message: 'Phone number is required' },
    { validate: validatePhoneNumber, message: 'Please enter a valid phone number' },
  ],
};

/**
 * Validates an entire form object
 */
export const validateForm = <T extends Record<string, any>>(
  formData: T,
  validationSchema: Record<keyof T, ValidationRule[]>
): Record<keyof T, string | null> => {
  const errors: Record<keyof T, string | null> = {} as Record<keyof T, string | null>;
  
  for (const field in validationSchema) {
    const validator = createValidator(validationSchema[field]);
    const value = formData[field];
    errors[field] = validator(typeof value === 'string' ? value : String(value || ''));
  }
  
  return errors;
};

/**
 * Checks if a form has any validation errors
 */
export const hasValidationErrors = (errors: Record<string, string | null>): boolean => {
  return Object.values(errors).some(error => error !== null);
};
