import api from './api';
import { AxiosError } from 'axios';
import type {
  RegisterData,
  LoginData,
  User,
  AuthResponse,
  LoginResponse
} from '../types/auth.types';

// Helper function to check if error is an AxiosError
function isAxiosError(error: any): error is AxiosError {
  return error.isAxiosError === true || error instanceof AxiosError;
}

class AuthService {
  async register(userData: RegisterData): Promise<AuthResponse<User>> {
    try {
      const response = await api.post('/api/auth/register', userData);
      return {
        success: true,
        data: response.data,
        error: null
      };
    } catch (error) {
      if (isAxiosError(error) && error.response) {
        const errorData = error.response.data as { error?: string };
        const errorMessage = errorData?.error || 'Registration failed';
        return {
          success: false,
          data: null,
          error: errorMessage
        };
      }
      return {
        success: false,
        data: null,
        error: 'Network error occurred. Please try again.'
      };
    }
  }

  async login(credentials: LoginData): Promise<AuthResponse<LoginResponse>> {
    try {
      const response = await api.post('/api/auth/login', credentials);
      return {
        success: true,
        data: response.data,
        error: null
      };
    } catch (error) {
      if (isAxiosError(error) && error.response) {
        const errorData = error.response.data as { error?: string };
        const errorMessage = errorData?.error || 'Login failed';
        return {
          success: false,
          data: null,
          error: errorMessage
        };
      }
      return {
        success: false,
        data: null,
        error: 'Network error occurred. Please try again.'
      };
    }
  }
}

const authService = new AuthService();

export { authService };
export default authService;

// Re-export types for convenience
export type {
  RegisterData,
  LoginData,
  User,
  AuthResponse,
  LoginResponse
} from '../types/auth.types';
