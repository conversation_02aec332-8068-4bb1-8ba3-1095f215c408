import api from './api';
import { AxiosError } from 'axios';

// Helper function to check if error is an AxiosError
function isAxiosError(error: any): error is AxiosError {
  return error.isAxiosError === true || error instanceof AxiosError;
}

export interface RegisterData {
  email: string;
  password: string;
  userType: 'Teacher' | 'Student' | 'Admin';
  name: {
    first: string;
    last: string;
  };
}

export interface LoginData {
  email: string;
  password: string;
}

export interface User {
  _id: string;
  email: string;
  userType: 'Teacher' | 'Student' | 'Admin';
  name: {
    first: string;
    last: string;
  };
  accountStatus: 'Active' | 'Suspended';
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse<T> {
  success: boolean;
  data: T | null;
  error: string | null;
}

export interface LoginResponse {
  token: string;
}

class AuthService {
  async register(userData: RegisterData): Promise<AuthResponse<User>> {
    try {
      const response = await api.post('/api/auth/register', userData);
      return {
        success: true,
        data: response.data,
        error: null
      };
    } catch (error) {
      if (isAxiosError(error) && error.response) {
        const errorData = error.response.data as { error?: string };
        const errorMessage = errorData?.error || 'Registration failed';
        return {
          success: false,
          data: null,
          error: errorMessage
        };
      }
      return {
        success: false,
        data: null,
        error: 'Network error occurred. Please try again.'
      };
    }
  }

  async login(credentials: LoginData): Promise<AuthResponse<LoginResponse>> {
    try {
      const response = await api.post('/api/auth/login', credentials);
      return {
        success: true,
        data: response.data,
        error: null
      };
    } catch (error) {
      if (isAxiosError(error) && error.response) {
        const errorData = error.response.data as { error?: string };
        const errorMessage = errorData?.error || 'Login failed';
        return {
          success: false,
          data: null,
          error: errorMessage
        };
      }
      return {
        success: false,
        data: null,
        error: 'Network error occurred. Please try again.'
      };
    }
  }
}

export const authService = new AuthService();
export default authService;
