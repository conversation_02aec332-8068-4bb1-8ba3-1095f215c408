import { authService, RegisterData, LoginData } from '../auth.service';

// Mock the api module
jest.mock('../api', () => ({
  __esModule: true,
  default: {
    post: jest.fn(),
  },
}));

import api from '../api';
const mockedApi = api as jest.Mocked<typeof api>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const mockRegisterData: RegisterData = {
      email: '<EMAIL>',
      password: 'securepass123',
      userType: 'Teacher',
      name: {
        first: '<PERSON>',
        last: '<PERSON>e'
      }
    };

    const mockSuccessResponse = {
      data: {
        _id: '507f1f77bcf86cd799439011',
        email: '<EMAIL>',
        userType: 'Teacher',
        name: {
          first: '<PERSON>',
          last: 'Doe'
        },
        accountStatus: 'Active',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      }
    };

    it('should register a user successfully', async () => {
      mockedApi.post.mockResolvedValueOnce(mockSuccessResponse);

      const result = await authService.register(mockRegisterData);

      expect(mockedApi.post).toHaveBeenCalledWith('/api/auth/register', mockRegisterData);
      expect(result).toEqual({
        success: true,
        data: mockSuccessResponse.data,
        error: null
      });
    });

    it('should handle registration error - user already exists', async () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          status: 409,
          data: { error: 'User with this email already exists' }
        }
      };
      mockedApi.post.mockRejectedValueOnce(axiosError);

      const result = await authService.register(mockRegisterData);

      expect(result).toEqual({
        success: false,
        data: null,
        error: 'User with this email already exists'
      });
    });

    it('should handle registration error - validation error', async () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: { error: 'Missing required fields' }
        }
      };
      mockedApi.post.mockRejectedValueOnce(axiosError);

      const result = await authService.register(mockRegisterData);

      expect(result).toEqual({
        success: false,
        data: null,
        error: 'Missing required fields'
      });
    });

    it('should handle network error', async () => {
      const networkError = new Error('Network Error');
      mockedApi.post.mockRejectedValueOnce(networkError);

      const result = await authService.register(mockRegisterData);

      expect(result).toEqual({
        success: false,
        data: null,
        error: 'Network error occurred. Please try again.'
      });
    });
  });

  describe('login', () => {
    const mockLoginData: LoginData = {
      email: '<EMAIL>',
      password: 'securepass123'
    };

    const mockSuccessResponse = {
      data: {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
      }
    };

    it('should login successfully', async () => {
      mockedApi.post.mockResolvedValueOnce(mockSuccessResponse);

      const result = await authService.login(mockLoginData);

      expect(mockedApi.post).toHaveBeenCalledWith('/api/auth/login', mockLoginData);
      expect(result).toEqual({
        success: true,
        data: mockSuccessResponse.data,
        error: null
      });
    });

    it('should handle login error - invalid credentials', async () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          status: 401,
          data: { error: 'Invalid email or password' }
        }
      };
      mockedApi.post.mockRejectedValueOnce(axiosError);

      const result = await authService.login(mockLoginData);

      expect(result).toEqual({
        success: false,
        data: null,
        error: 'Invalid email or password'
      });
    });

    it('should handle login error - missing fields', async () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: { error: 'Missing email or password' }
        }
      };
      mockedApi.post.mockRejectedValueOnce(axiosError);

      const result = await authService.login(mockLoginData);

      expect(result).toEqual({
        success: false,
        data: null,
        error: 'Missing email or password'
      });
    });

    it('should handle server error', async () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          status: 500,
          data: { error: 'Server error' }
        }
      };
      mockedApi.post.mockRejectedValueOnce(axiosError);

      const result = await authService.login(mockLoginData);

      expect(result).toEqual({
        success: false,
        data: null,
        error: 'Server error'
      });
    });
  });
});
