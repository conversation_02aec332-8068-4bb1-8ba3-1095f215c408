import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { selectAuth, selectIsAuthenticated } from '../store/auth.slice';
import type { User } from '../types/auth.types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
  authCheck?: (isAuthenticated: boolean, user: User | null) => boolean;
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo = '/login',
  authCheck,
  fallback,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { status, user } = useSelector(selectAuth);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  useEffect(() => {
    // Don't redirect while loading
    if (status === 'loading') {
      return;
    }

    // Determine if user should have access
    let hasAccess = false;

    if (authCheck) {
      // Use custom authentication check
      hasAccess = authCheck(isAuthenticated, user);
    } else {
      // Default authentication check - user must be authenticated and have user data
      hasAccess = isAuthenticated && user !== null;
    }

    if (!hasAccess) {
      // Redirect to login with current location for redirect after login
      const from = location.pathname + location.search;
      navigate(redirectTo, { 
        state: { from } 
      });
    }
  }, [isAuthenticated, user, status, navigate, redirectTo, location, authCheck]);

  // Show loading spinner while checking authentication
  if (status === 'loading') {
    return (
      fallback || (
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          minHeight="50vh"
          gap={2}
        >
          <CircularProgress size={40} />
          <Typography variant="body1" color="text.secondary">
            Checking authentication...
          </Typography>
        </Box>
      )
    );
  }

  // Determine if user should have access
  let hasAccess = false;

  if (authCheck) {
    // Use custom authentication check
    hasAccess = authCheck(isAuthenticated, user);
  } else {
    // Default authentication check
    hasAccess = isAuthenticated && user !== null;
  }

  // Render children if user has access
  if (hasAccess) {
    return <>{children}</>;
  }

  // Return null while redirecting (useEffect will handle the redirect)
  return null;
};

export default ProtectedRoute;

// Convenience components for common role-based access patterns
export const TeacherRoute: React.FC<Omit<ProtectedRouteProps, 'authCheck'>> = (props) => {
  const teacherCheck = (isAuthenticated: boolean, user: User | null) => {
    return isAuthenticated && user?.userType === 'Teacher';
  };

  return <ProtectedRoute {...props} authCheck={teacherCheck} />;
};

export const StudentRoute: React.FC<Omit<ProtectedRouteProps, 'authCheck'>> = (props) => {
  const studentCheck = (isAuthenticated: boolean, user: User | null) => {
    return isAuthenticated && user?.userType === 'Student';
  };

  return <ProtectedRoute {...props} authCheck={studentCheck} />;
};

export const AdminRoute: React.FC<Omit<ProtectedRouteProps, 'authCheck'>> = (props) => {
  const adminCheck = (isAuthenticated: boolean, user: User | null) => {
    return isAuthenticated && user?.userType === 'Admin';
  };

  return <ProtectedRoute {...props} authCheck={adminCheck} />;
};
