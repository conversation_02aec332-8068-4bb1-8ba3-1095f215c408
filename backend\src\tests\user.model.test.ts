import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import User from '../models/user.model';

jest.setTimeout(30000); // 30 seconds

describe('User Model', () => {
  let mongoServer: MongoMemoryServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    await mongoose.connect(mongoServer.getUri(), {});
  });

  afterAll(async () => {
    await mongoose.disconnect();
    if (mongoServer) {
      await mongoServer.stop();
    }
  });

  afterEach(async () => {
    await User.deleteMany({});
  });

  it('should require email, password, userType, and name', async () => {
    const user = new User();
    let err;
    try {
      await user.validate();
    } catch (error) {
      if (error instanceof mongoose.Error.ValidationError) {
        expect(error.errors.email).toBeDefined();
        expect(error.errors.password).toBeDefined();
        expect(error.errors.userType).toBeDefined();
        expect(error.errors['name.first']).toBeDefined();
        expect(error.errors['name.last']).toBeDefined();
      } else {
        throw error;
      }
    }
  });

  it('should hash the password before saving', async () => {
    const user = new User({
      email: '<EMAIL>',
      password: 'plaintextpassword',
      userType: 'Teacher',
      name: { first: 'Test', last: 'User' },
      accountStatus: 'Active',
    });
    await user.save();
    expect(user.password).not.toBe('plaintextpassword');
    expect(user.password.length).toBeGreaterThan(20);
  });

  it('should compare passwords correctly', async () => {
    const user = new User({
      email: '<EMAIL>',
      password: 'mypassword',
      userType: 'Teacher',
      name: { first: 'Jane', last: 'Doe' },
      accountStatus: 'Active',
    });
    await user.save();
    const isMatch = await user.comparePassword('mypassword');
    const isNotMatch = await user.comparePassword('wrongpassword');
    expect(isMatch).toBe(true);
    expect(isNotMatch).toBe(false);
  });
});
