import axios from 'axios';

// Handle both Vite environment and Jest test environment
const getBaseURL = () => {
  // In test environment, use the mocked value from setupTests.ts
  if (typeof globalThis !== 'undefined' && globalThis.import?.meta?.env?.VITE_API_BASE_URL) {
    return globalThis.import.meta.env.VITE_API_BASE_URL;
  }
  // In Vite environment
  if (typeof import !== 'undefined' && import.meta?.env?.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL;
  }
  // Fallback for test environment
  return 'http://localhost:3000';
};

const api = axios.create({
  baseURL: getBaseURL(),
  withCredentials: true, // for cookies/JWT if needed
});

export default api;
