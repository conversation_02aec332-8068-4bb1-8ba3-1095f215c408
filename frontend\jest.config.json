{"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "moduleFileExtensions": ["js", "jsx", "ts", "tsx"], "testMatch": ["**/__tests__/**/*.test.(ts|tsx)", "**/?(*.)+(spec|test).(ts|tsx)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"tsconfig": {"module": "commonjs", "target": "es2020", "esModuleInterop": true, "allowSyntheticDefaultImports": true}}]}, "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "extensionsToTreatAsEsm": []}