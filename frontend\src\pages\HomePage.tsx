import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Container,
  Typography,
  Box,
  Button,
  Stack,
  AppBar,
  Toolbar,
  Paper
} from '@mui/material';
import { Logout as LogoutIcon } from '@mui/icons-material';
import { selectIsAuthenticated, selectUser, logout } from '../store/auth.slice';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const user = useSelector(selectUser);



  return (
    <>
      {/* Navigation Bar */}
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            ComicGenius
          </Typography>
          {isAuthenticated ? (
            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2">
                Welcome, {user?.name.first}!
              </Typography>
              <Button color="inherit" onClick={() => navigate('/dashboard')}>
                Dashboard
              </Button>
              <Button
                color="inherit"
                onClick={() => {
                  dispatch(logout());
                  navigate('/login');
                }}
                startIcon={<LogoutIcon />}
              >
                Logout
              </Button>
            </Stack>
          ) : (
            <Stack direction="row" spacing={2}>
              <Button color="inherit" component={Link} to="/login">
                Login
              </Button>
              <Button color="inherit" component={Link} to="/register">
                Register
              </Button>
            </Stack>
          )}
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Container maxWidth="md">
        <Box sx={{ mt: 8, textAlign: 'center' }}>
          <Typography variant="h2" component="h1" gutterBottom>
            ComicGenius
          </Typography>
          <Typography variant="h5" color="text.secondary" sx={{ mb: 2 }}>
            Welcome! Create, assign, and grade educational comics for your classroom.
          </Typography>

          {!isAuthenticated && (
            <Paper elevation={3} sx={{ p: 4, mt: 4 }}>
              <Typography variant="h6" gutterBottom>
                Get Started
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Join ComicGenius to create engaging educational content for your students.
              </Typography>
              <Stack direction="row" spacing={2} justifyContent="center">
                <Button
                  variant="contained"
                  size="large"
                  component={Link}
                  to="/register"
                >
                  Sign Up
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  component={Link}
                  to="/login"
                >
                  Sign In
                </Button>
              </Stack>
            </Paper>
          )}

          {isAuthenticated && (
            <Paper elevation={3} sx={{ p: 4, mt: 4 }}>
              <Typography variant="h6" gutterBottom>
                Welcome back, {user?.name.first}!
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Ready to create some amazing educational comics?
              </Typography>
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate('/dashboard')}
              >
                Go to Dashboard
              </Button>
            </Paper>
          )}
        </Box>
      </Container>
    </>
  );
};

export default HomePage;
