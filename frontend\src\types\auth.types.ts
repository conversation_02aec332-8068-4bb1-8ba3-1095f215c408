/**
 * Authentication-related type definitions
 */

export interface RegisterData {
  email: string;
  password: string;
  userType: 'Teacher' | 'Student' | 'Admin';
  name: {
    first: string;
    last: string;
  };
}

export interface LoginData {
  email: string;
  password: string;
}

export interface User {
  _id: string;
  email: string;
  userType: 'Teacher' | 'Student' | 'Admin';
  name: {
    first: string;
    last: string;
  };
  accountStatus: 'Active' | 'Suspended';
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse<T> {
  success: boolean;
  data: T | null;
  error: string | null;
}

export interface LoginResponse {
  token: string;
}
