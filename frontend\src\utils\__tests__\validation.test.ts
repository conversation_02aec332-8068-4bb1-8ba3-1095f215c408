import {
  validateEmail,
  validatePassword,
  validateRequired,
  validatePasswordMatch,
  validateName,
  createValidator,
  ValidationRule,
} from '../validation';

describe('Validation Utils', () => {
  describe('validateEmail', () => {
    it('should return null for valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBeNull();
      });
    });

    it('should return error message for invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe('Please enter a valid email address');
      expect(validateEmail('@example.com')).toBe('Please enter a valid email address');
      expect(validateEmail('user@')).toBe('Please enter a valid email address');
      expect(validateEmail('user@.com')).toBe('Please enter a valid email address');
      expect(validateEmail('user <EMAIL>')).toBe('Please enter a valid email address');
    });

    it('should return error message for empty email', () => {
      expect(validateEmail('')).toBe('Please enter a valid email address');
    });

    it('should return custom error message when provided', () => {
      const customMessage = 'Custom email error';
      expect(validateEmail('invalid', customMessage)).toBe(customMessage);
    });
  });

  describe('validatePassword', () => {
    it('should return null for valid passwords', () => {
      const validPasswords = [
        'password123',
        'mySecurePass',
        '123456',
        'a'.repeat(6),
      ];

      validPasswords.forEach(password => {
        expect(validatePassword(password)).toBeNull();
      });
    });

    it('should return error message for passwords that are too short', () => {
      const shortPasswords = ['', '12345', 'abc', 'a'.repeat(5)];

      shortPasswords.forEach(password => {
        expect(validatePassword(password)).toBe('Password must be at least 6 characters long');
      });
    });

    it('should validate with custom minimum length', () => {
      expect(validatePassword('12345678', undefined, 8)).toBeNull();
      expect(validatePassword('1234567', undefined, 8)).toBe('Password must be at least 8 characters long');
    });

    it('should return custom error message when provided', () => {
      const customMessage = 'Custom password error';
      expect(validatePassword('123', customMessage)).toBe(customMessage);
    });
  });

  describe('validateRequired', () => {
    it('should return null for non-empty values', () => {
      const validValues = ['test', 'a', '123', 'valid input'];

      validValues.forEach(value => {
        expect(validateRequired(value)).toBeNull();
      });
    });

    it('should return error message for empty values', () => {
      const emptyValues = ['', '   ', '\t', '\n'];

      emptyValues.forEach(value => {
        expect(validateRequired(value)).toBe('This field is required');
      });
    });

    it('should return custom error message when provided', () => {
      const customMessage = 'Custom required error';
      expect(validateRequired('', customMessage)).toBe(customMessage);
    });

    it('should return custom field name in error message', () => {
      expect(validateRequired('', undefined, 'Email')).toBe('Email is required');
    });
  });

  describe('validatePasswordMatch', () => {
    it('should return null when passwords match', () => {
      expect(validatePasswordMatch('password123', 'password123')).toBeNull();
      expect(validatePasswordMatch('', '')).toBeNull();
    });

    it('should return error message when passwords do not match', () => {
      expect(validatePasswordMatch('password123', 'password456')).toBe('Passwords do not match');
      expect(validatePasswordMatch('abc', 'def')).toBe('Passwords do not match');
    });

    it('should return custom error message when provided', () => {
      const customMessage = 'Custom password match error';
      expect(validatePasswordMatch('abc', 'def', customMessage)).toBe(customMessage);
    });
  });

  describe('validateName', () => {
    it('should return null for valid names', () => {
      const validNames = ['John', 'Mary-Jane', "O'Connor", 'José', '李'];

      validNames.forEach(name => {
        expect(validateName(name)).toBeNull();
      });
    });

    it('should return error message for invalid names', () => {
      const invalidNames = ['', '   ', 'John123', 'Name@', 'Name!'];

      invalidNames.forEach(name => {
        expect(validateName(name)).toBe('Please enter a valid name (letters, spaces, hyphens, and apostrophes only)');
      });
    });

    it('should return custom error message when provided', () => {
      const customMessage = 'Custom name error';
      expect(validateName('123', customMessage)).toBe(customMessage);
    });
  });

  describe('createValidator', () => {
    it('should create a validator function that applies multiple rules', () => {
      const rules: ValidationRule[] = [
        { validate: validateRequired, message: 'Field is required' },
        { validate: validateEmail, message: 'Invalid email' },
      ];

      const validator = createValidator(rules);

      expect(validator('')).toBe('Field is required');
      expect(validator('invalid-email')).toBe('Invalid email');
      expect(validator('<EMAIL>')).toBeNull();
    });

    it('should return first validation error encountered', () => {
      const rules: ValidationRule[] = [
        { validate: validateRequired, message: 'Required' },
        { validate: (value) => value.length < 5 ? 'Too short' : null, message: 'Too short' },
      ];

      const validator = createValidator(rules);

      expect(validator('')).toBe('Required');
      expect(validator('abc')).toBe('Too short');
      expect(validator('valid')).toBeNull();
    });

    it('should handle custom validation functions', () => {
      const customRule = (value: string) => {
        return value.includes('test') ? null : 'Must contain "test"';
      };

      const rules: ValidationRule[] = [
        { validate: customRule, message: 'Custom error' },
      ];

      const validator = createValidator(rules);

      expect(validator('testing')).toBeNull();
      expect(validator('invalid')).toBe('Custom error');
    });
  });
});
