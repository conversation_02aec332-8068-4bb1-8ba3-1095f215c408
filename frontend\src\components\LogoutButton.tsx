import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Button,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  IconButton,
} from '@mui/material';
import { Logout as LogoutIcon } from '@mui/icons-material';
import { logout } from '../store/auth.slice';

interface LogoutButtonProps {
  variant?: 'button' | 'menuItem' | 'iconButton';
  confirmLogout?: boolean;
  children?: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

const LogoutButton: React.FC<LogoutButtonProps> = ({
  variant = 'button',
  confirmLogout = false,
  children,
  onClick,
  className,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
    if (onClick) {
      onClick();
    }
  };

  const handleClick = () => {
    if (confirmLogout) {
      setShowConfirmDialog(true);
    } else {
      handleLogout();
    }
  };

  const handleConfirmLogout = () => {
    setShowConfirmDialog(false);
    handleLogout();
  };

  const handleCancelLogout = () => {
    setShowConfirmDialog(false);
  };

  const buttonText = children || 'Logout';

  const renderButton = () => {
    switch (variant) {
      case 'menuItem':
        return (
          <MenuItem onClick={handleClick} className={className}>
            <LogoutIcon sx={{ mr: 1 }} />
            {buttonText}
          </MenuItem>
        );

      case 'iconButton':
        return (
          <IconButton
            onClick={handleClick}
            className={className}
            color="inherit"
            title={typeof buttonText === 'string' ? buttonText : 'Logout'}
          >
            <LogoutIcon />
          </IconButton>
        );

      default:
        return (
          <Button
            onClick={handleClick}
            className={className}
            variant="outlined"
            startIcon={<LogoutIcon />}
          >
            {buttonText}
          </Button>
        );
    }
  };

  return (
    <>
      {renderButton()}

      {/* Confirmation Dialog */}
      <Dialog
        open={showConfirmDialog}
        onClose={handleCancelLogout}
        aria-labelledby="logout-dialog-title"
        aria-describedby="logout-dialog-description"
      >
        <DialogTitle id="logout-dialog-title">
          Confirm Logout
        </DialogTitle>
        <DialogContent>
          <Typography id="logout-dialog-description">
            Are you sure you want to logout? You will need to sign in again to access your account.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelLogout} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmLogout}
            color="primary"
            variant="contained"
            startIcon={<LogoutIcon />}
          >
            Logout
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default LogoutButton;
