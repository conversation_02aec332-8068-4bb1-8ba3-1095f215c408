import { configureStore } from '@reduxjs/toolkit';
import authReducer, {
  AuthState,
  registerUser,
  loginUser,
  logout,
  clearError,
  selectAuth,
  selectIsAuthenticated,
  selectUser,
  selectAuthStatus,
  selectAuthError
} from '../auth.slice';
import { authService } from '../../services/auth.service';

// Mock the api module first
jest.mock('../../services/api', () => ({
  __esModule: true,
  default: {
    post: jest.fn(),
  },
}));

// Mock the auth service
jest.mock('../../services/auth.service');
const mockedAuthService = authService as jest.Mocked<typeof authService>;

type RootState = {
  auth: AuthState;
};

describe('authSlice', () => {
  let store: ReturnType<typeof configureStore<RootState>>;

  beforeEach(() => {
    // Clear localStorage before creating store
    (localStorage.getItem as jest.Mock).mockReturnValue(null);
    (localStorage.setItem as jest.Mock).mockClear();
    (localStorage.removeItem as jest.Mock).mockClear();
    (localStorage.clear as jest.Mock).mockClear();

    store = configureStore<RootState>({
      reducer: {
        auth: authReducer,
      },
    });
    jest.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().auth;
      expect(state).toEqual({
        user: null,
        token: null,
        status: 'idle',
        error: null,
      });
    });

    it('should load token from localStorage on initialization', () => {
      const mockUser = {
        _id: '123',
        email: '<EMAIL>',
        userType: 'Teacher',
        name: { first: 'John', last: 'Doe' },
        accountStatus: 'Active',
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01'
      };

      (localStorage.getItem as jest.Mock).mockImplementation((key: string) => {
        if (key === 'token') return 'test-token';
        if (key === 'user') return JSON.stringify(mockUser);
        return null;
      });

      const newStore = configureStore<RootState>({
        reducer: {
          auth: authReducer,
        },
      });

      const state = newStore.getState().auth;
      expect(state.token).toBe('test-token');
      expect(state.user).toEqual(mockUser);
    });
  });

  describe('registerUser async thunk', () => {
    const mockRegisterData = {
      email: '<EMAIL>',
      password: 'password123',
      userType: 'Teacher' as const,
      name: { first: 'John', last: 'Doe' }
    };

    const mockUser = {
      _id: '123',
      email: '<EMAIL>',
      userType: 'Teacher' as const,
      name: { first: 'John', last: 'Doe' },
      accountStatus: 'Active' as const,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01'
    };

    it('should handle successful registration', async () => {
      mockedAuthService.register.mockResolvedValueOnce({
        success: true,
        data: mockUser,
        error: null
      });

      await store.dispatch(registerUser(mockRegisterData));
      const state = store.getState().auth;

      expect(state.status).toBe('succeeded');
      expect(state.user).toEqual(mockUser);
      expect(state.error).toBeNull();
    });

    it('should handle registration failure', async () => {
      mockedAuthService.register.mockResolvedValueOnce({
        success: false,
        data: null,
        error: 'User already exists'
      });

      await store.dispatch(registerUser(mockRegisterData));
      const state = store.getState().auth;

      expect(state.status).toBe('failed');
      expect(state.user).toBeNull();
      expect(state.error).toBe('User already exists');
    });

    it('should set loading state during registration', () => {
      mockedAuthService.register.mockImplementation(() => new Promise(() => {}));
      
      store.dispatch(registerUser(mockRegisterData));
      const state = store.getState().auth;

      expect(state.status).toBe('loading');
      expect(state.error).toBeNull();
    });
  });

  describe('loginUser async thunk', () => {
    const mockLoginData = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const mockLoginResponse = {
      token: 'jwt-token-123'
    };

    it('should handle successful login', async () => {
      mockedAuthService.login.mockResolvedValueOnce({
        success: true,
        data: mockLoginResponse,
        error: null
      });

      await store.dispatch(loginUser(mockLoginData));
      const state = store.getState().auth;

      expect(state.status).toBe('succeeded');
      expect(state.token).toBe('jwt-token-123');
      expect(state.error).toBeNull();
      expect(localStorage.setItem).toHaveBeenCalledWith('token', 'jwt-token-123');
    });

    it('should handle login failure', async () => {
      mockedAuthService.login.mockResolvedValueOnce({
        success: false,
        data: null,
        error: 'Invalid credentials'
      });

      await store.dispatch(loginUser(mockLoginData));
      const state = store.getState().auth;

      expect(state.status).toBe('failed');
      expect(state.token).toBeNull();
      expect(state.error).toBe('Invalid credentials');
    });
  });

  describe('logout action', () => {
    it('should clear auth state and localStorage', () => {
      store.dispatch(logout());
      const state = store.getState().auth;

      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.status).toBe('idle');
      expect(state.error).toBeNull();
      expect(localStorage.removeItem).toHaveBeenCalledWith('token');
      expect(localStorage.removeItem).toHaveBeenCalledWith('user');
    });
  });

  describe('clearError action', () => {
    it('should clear error state', () => {
      // First set an error
      store.dispatch({ type: 'auth/registerUser/rejected', payload: 'Test error' });
      
      store.dispatch(clearError());
      const state = store.getState().auth;

      expect(state.error).toBeNull();
    });
  });

  describe('selectors', () => {
    it('should select auth state', () => {
      const state = store.getState();
      const authState = selectAuth(state);
      expect(authState).toEqual(state.auth);
    });

    it('should select isAuthenticated correctly', () => {
      const state = store.getState();
      expect(selectIsAuthenticated(state)).toBe(false);

      // Set token
      store.dispatch({ type: 'auth/loginUser/fulfilled', payload: { token: 'test-token' } });
      const newState = store.getState();
      expect(selectIsAuthenticated(newState)).toBe(true);
    });

    it('should select user', () => {
      const state = store.getState();
      expect(selectUser(state)).toBeNull();
    });

    it('should select auth status', () => {
      const state = store.getState();
      expect(selectAuthStatus(state)).toBe('idle');
    });

    it('should select auth error', () => {
      const state = store.getState();
      expect(selectAuthError(state)).toBeNull();
    });
  });
});
