import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import { authService } from '../services/auth.service';
import type { RegisterData, LoginData, User, LoginResponse } from '../types/auth.types';

export interface AuthState {
  user: User | null;
  token: string | null;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

// Helper functions for localStorage
const getStoredToken = (): string | null => {
  try {
    return localStorage.getItem('token');
  } catch {
    return null;
  }
};

const getStoredUser = (): User | null => {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch {
    return null;
  }
};

const setStoredAuth = (token: string, user?: User) => {
  try {
    localStorage.setItem('token', token);
    if (user) {
      localStorage.setItem('user', JSON.stringify(user));
    }
  } catch {
    // Ignore localStorage errors
  }
};

const clearStoredAuth = () => {
  try {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  } catch {
    // Ignore localStorage errors
  }
};

// Initial state - load from localStorage if available
// If we have a token but no user, clear the token (corrupted state)
const storedToken = getStoredToken();
const storedUser = getStoredUser();

const initialState: AuthState = {
  user: (storedToken && storedUser) ? storedUser : null,
  token: (storedToken && storedUser) ? storedToken : null,
  status: 'idle',
  error: null,
};

// Clear localStorage if we have inconsistent state
if (storedToken && !storedUser) {
  localStorage.removeItem('token');
} else if (!storedToken && storedUser) {
  localStorage.removeItem('user');
}

// Async thunks
export const registerUser = createAsyncThunk(
  'auth/registerUser',
  async (userData: RegisterData, { rejectWithValue }) => {
    const result = await authService.register(userData);
    if (result.success && result.data) {
      return result.data;
    } else {
      return rejectWithValue(result.error);
    }
  }
);

export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: LoginData, { rejectWithValue }) => {
    const result = await authService.login(credentials);
    console.log('Auth service result:', result);
    console.log('Auth service result.data:', result.data);
    if (result.success && result.data) {
      console.log('Returning from thunk:', result.data);
      return result.data;
    } else {
      return rejectWithValue(result.error);
    }
  }
);

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.status = 'idle';
      state.error = null;
      clearStoredAuth();
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Register user
      .addCase(registerUser.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.user = action.payload;
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      // Login user
      .addCase(loginUser.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        console.log('Login fulfilled with payload:', action.payload);
        console.log('Payload token:', action.payload.token);
        console.log('Payload user:', action.payload.user);
        console.log('Payload keys:', Object.keys(action.payload));

        // Try to access user data with different possible structures
        const payload = action.payload as any;
        console.log('Trying payload.user:', payload.user);
        console.log('Trying payload.data?.user:', payload.data?.user);
        console.log('Full payload structure:', JSON.stringify(payload, null, 2));

        state.status = 'succeeded';
        state.token = action.payload.token;
        state.user = action.payload.user;
        state.error = null;
        setStoredAuth(action.payload.token, action.payload.user);
        console.log('Auth state after login:', { token: state.token, user: state.user });
      })
      .addCase(loginUser.rejected, (state, action) => {
        console.log('Login rejected with error:', action.payload);
        state.status = 'failed';
        state.error = action.payload as string;
      });
  },
});

// Actions
export const { logout, clearError } = authSlice.actions;

// Selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectIsAuthenticated = (state: { auth: AuthState }) =>
  !!state.auth.token && !!state.auth.user;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectAuthStatus = (state: { auth: AuthState }) => state.auth.status;
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error;

export default authSlice.reducer;
