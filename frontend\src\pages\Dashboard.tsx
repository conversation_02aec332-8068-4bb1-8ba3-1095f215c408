import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Container,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  Stack,
  Chip,
} from '@mui/material';
import {
  Home as HomeIcon,
  School as SchoolIcon,
  Assignment as AssignmentIcon,
  Create as CreateIcon,
} from '@mui/icons-material';
import { selectUser } from '../store/auth.slice';
import LogoutButton from '../components/LogoutButton';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const user = useSelector(selectUser);

  const getUserTypeColor = (userType: string) => {
    switch (userType) {
      case 'Teacher': return 'primary';
      case 'Student': return 'secondary';
      case 'Admin': return 'error';
      default: return 'default';
    }
  };

  return (
    <>
      {/* Navigation Bar */}
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            ComicGenius Dashboard
          </Typography>
          <Stack direction="row" spacing={2} alignItems="center">
            <Button 
              color="inherit" 
              startIcon={<HomeIcon />}
              onClick={() => navigate('/')}
            >
              Home
            </Button>
            <Typography variant="body2">
              {user?.name.first} {user?.name.last}
            </Typography>
            <Chip 
              label={user?.userType} 
              color={getUserTypeColor(user?.userType || '')}
              size="small"
              variant="outlined"
              sx={{ color: 'white', borderColor: 'white' }}
            />
            <LogoutButton variant="button" />
          </Stack>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Welcome back, {user?.name.first}!
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Ready to create some amazing educational comics?
          </Typography>
        </Box>

        {/* Quick Actions */}
        <Grid container spacing={3}>
          {user?.userType === 'Teacher' && (
            <>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ height: '100%', cursor: 'pointer' }} onClick={() => alert('Create Assignment - Coming Soon!')}>
                  <CardContent sx={{ textAlign: 'center', py: 3 }}>
                    <AssignmentIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      Create Assignment
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Set up new comic assignments for your students
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ height: '100%', cursor: 'pointer' }} onClick={() => alert('View Classes - Coming Soon!')}>
                  <CardContent sx={{ textAlign: 'center', py: 3 }}>
                    <SchoolIcon sx={{ fontSize: 48, color: 'secondary.main', mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      My Classes
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Manage your classes and students
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </>
          )}
          
          {user?.userType === 'Student' && (
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: '100%', cursor: 'pointer' }} onClick={() => alert('My Assignments - Coming Soon!')}>
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <AssignmentIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    My Assignments
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    View and complete your comic assignments
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ height: '100%', cursor: 'pointer' }} onClick={() => alert('Create Comic - Coming Soon!')}>
              <CardContent sx={{ textAlign: 'center', py: 3 }}>
                <CreateIcon sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Create Comic
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Start creating your educational comic
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Recent Activity */}
        <Box sx={{ mt: 4 }}>
          <Typography variant="h5" gutterBottom>
            Recent Activity
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
              No recent activity yet. Start by creating your first comic or assignment!
            </Typography>
          </Paper>
        </Box>
      </Container>
    </>
  );
};

export default Dashboard;
