{"name": "<PERSON><PERSON><PERSON>-backend", "version": "1.0.0", "description": "Backend for ComicGenius platform", "main": "src/index.ts", "scripts": {"start": "ts-node src/index.ts", "dev": "nodemon src/index.ts", "test": "jest --passWithNoTests"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/mongodb-memory-server": "^1.8.0", "@types/node": "^24.0.4", "@types/supertest": "^6.0.3", "jest": "^30.0.3", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.10", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0"}}