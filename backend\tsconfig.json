{"compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "preset": "ts-jest", "testEnvironment": "node", "transform": {"^.+\\.tsx?$": ["ts-jest", {"tsconfig": "tsconfig.json"}]}}