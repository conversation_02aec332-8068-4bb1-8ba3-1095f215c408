# ComicGenius: Development Blueprint & LLM Prompts

This document outlines the step-by-step implementation plan for the ComicGenius platform. Each step is presented as a self-contained prompt for a code-generation LLM, designed to build the application incrementally using Test-Driven Development (TDD).

## Phase 1: MVP - Core Functionality

### Milestone 1: Project Setup & Foundation

**Context:** The first step is to establish the foundational structure for both the backend and frontend projects. This creates the workspace and configuration needed for all subsequent development.

```text
Prompt 1.1: Backend Project Initialization

Using Node.js, Express, and TypeScript, initialize a new backend project for the ComicGenius application.
1.  Create a standard project directory structure (`src`, `src/api`, `src/config`, `src/models`, `src/tests`).
2.  Initialize `package.json` and install necessary dependencies: `express`, `mongoose`, `jsonwebtoken`, `bcryptjs`, `dotenv`, `cors`.
3.  Install development dependencies: `typescript`, `ts-node`, `nodemon`, `@types/node`, `@types/express`, `@types/cors`, `jest`, `supertest`, `ts-jest`.
4.  Configure `tsconfig.json` for the project.
5.  Create a basic Express server in `src/index.ts` that listens on a port defined in a `.env` file and includes a simple health check route (`/health`).
6.  Add npm scripts for `start`, `dev`, and `test`.
```

```text
Prompt 1.2: Frontend Project Initialization

Using Vite, React, and TypeScript, initialize a new frontend project for the ComicGenius application.
1.  Use Vite to scaffold a new React project with the TypeScript template.
2.  Install necessary dependencies: `react-router-dom`, `axios`, `@reduxjs/toolkit`, `react-redux`, `@mui/material`, `@emotion/react`, `@emotion/styled`.
3.  Create a standard project directory structure (`src/components`, `src/pages`, `src/services`, `src/store`, `src/hooks`, `src/assets`).
4.  Configure basic client-side routing in `App.tsx` using `react-router-dom`, setting up a home page and a placeholder for a login page.
5.  Set up a basic Redux store using `@reduxjs/toolkit`.
```

### Milestone 2: Teacher Authentication (Vertical Slice)

**Context:** This milestone focuses on building the complete, end-to-end feature for a teacher to register and log in. We will start with the backend data model and API, write tests, and then build the corresponding frontend components.

```text
Prompt 2.1: User Data Model and Tests

In the backend project, define the Mongoose schema for the `User` model based on the specification.
1.  Create `src/models/user.model.ts`.
2.  The schema should include fields for `email` (unique, required, with validation), `password` (required), `userType` (Enum: 'Teacher', 'Student', 'Admin'), `name` (first, last), and `accountStatus` (Enum: 'Active', 'Suspended').
3.  Implement a pre-save middleware using `bcryptjs` to hash the user's password before saving it to the database.
4.  Add an instance method to the schema to compare candidate passwords with the hashed password.
5.  Write unit tests for the User model using Jest, ensuring the password hashing and comparison methods work correctly.
```

```text
Prompt 2.2: Backend Registration Endpoint and Tests

In the backend project, create the API endpoint for user registration.
1.  Create a new router file at `src/api/auth.routes.ts`.
2.  Implement a `POST /api/auth/register` endpoint.
3.  The controller logic should validate the incoming request body (email, password, name, userType).
4.  It should check if a user with the given email already exists. If so, return a 409 Conflict error.
5.  If the user does not exist, create a new `User` document and save it to the database.
6.  Return a 201 Created status with the new user's data (excluding the password).
7.  Write integration tests for this endpoint using Supertest and Jest, covering success cases, validation errors, and duplicate email errors.
```

```text
Prompt 2.3: Backend Login Endpoint and Tests

In the backend project, create the API endpoint for user login.
1.  In `src/api/auth.routes.ts`, implement a `POST /api/auth/login` endpoint.
2.  The controller should find the user by email. If not found, return a 401 Unauthorized error.
3.  Use the password comparison method on the user model to verify the password. If incorrect, return a 401 error.
4.  On successful authentication, generate a JSON Web Token (JWT) containing the user's ID and userType. Use a secret key and an expiration time from the `.env` file.
5.  Return a 200 OK status with the JWT.
6.  Write integration tests for this endpoint, covering success cases, incorrect email, and incorrect password scenarios.
```

```text
Prompt 2.4: Frontend API Service for Authentication

In the frontend project, create a service to handle communication with the backend authentication API.
1.  Create `src/services/auth.service.ts`.
2.  Use `axios` to create an API client instance with the base URL of the backend.
3.  Implement a `register` function that takes user data and makes a `POST` request to `/api/auth/register`.
4.  Implement a `login` function that takes credentials and makes a `POST` request to `/api/auth/login`.
5.  Handle potential errors and return data in a consistent format.
```

```text
Prompt 2.5: Frontend Redux Slice for Authentication

In the frontend project, create a Redux slice to manage authentication state.
1.  Create `src/store/auth.slice.ts`.
2.  Define the slice state to include `user`, `token`, and `status` (e.g., 'idle', 'loading', 'succeeded', 'failed').
3.  Create async thunks using `createAsyncThunk` for `registerUser` and `loginUser` that use the `auth.service`.
4.  Handle the lifecycle of these thunks (`pending`, `fulfilled`, `rejected`) in the `extraReducers` to update the state, storing the user and token in the state and in `localStorage` on successful login.
5.  Add a reducer to handle user logout, clearing the state and `localStorage`.
```

```text
Prompt 2.6: Frontend Registration and Login Pages

In the frontend project, create the UI components for registration and login.
1.  Create a `RegisterPage.tsx` in `src/pages`.
2.  Using Material-UI components (`TextField`, `Button`, `Container`), build a form to capture the user's name, email, and password.
3.  On form submission, dispatch the `registerUser` async thunk from the auth slice.
4.  Display loading indicators and error messages based on the Redux state.
5.  Create a `LoginPage.tsx` in `src/pages` with a similar form for email and password.
6.  On form submission, dispatch the `loginUser` async thunk.
7.  On successful login, redirect the user to a new, placeholder "Dashboard" page.
8.  Update `App.tsx` to include routes for `/register`, `/login`, and a protected `/dashboard` route that is only accessible if the user is logged in.
```

### Milestone 3: Class Management (Vertical Slice)

**Context:** Now that teachers can log in, the next logical step is to allow them to create and view their classes. This milestone builds out that core functionality.

```text
Prompt 3.1: Backend Class Model and API Middleware

In the backend project:
1.  Create a Mongoose schema for `Class` in `src/models/class.model.ts`. It should include `name`, `teacher` (ObjectId ref to User), `school` (ObjectId, optional for now), and `students` (Array of ObjectId refs).
2.  Create an authentication middleware in `src/middleware/auth.middleware.ts`. This middleware should verify the JWT from the `Authorization` header. If valid, it should attach the user's data (decoded from the token) to the request object. If invalid, it should return a 401 Unauthorized error.
3.  Create a second middleware to check if the authenticated user is a 'Teacher'.
```

```text
Prompt 3.2: Backend Class Endpoints and Tests

In the backend project, create the API endpoints for managing classes.
1.  Create `src/api/class.routes.ts`.
2.  Implement a `POST /api/classes` endpoint, protected by the auth and teacher-role middleware. The controller should create a new class, associating it with the authenticated teacher's ID.
3.  Implement a `GET /api/classes` endpoint, also protected, that retrieves all classes belonging to the authenticated teacher.
4.  Write integration tests for both endpoints, ensuring they are protected and function correctly for authenticated teachers. Test that non-teachers cannot access them.
```

```text
Prompt 3.3: Frontend Class Management UI

In the frontend project, build the UI for teachers to manage their classes.
1.  Create a `class.service.ts` to communicate with the `/api/classes` endpoints. Remember to include the auth token in the request headers.
2.  Create a `class.slice.ts` in the Redux store to manage class state, including thunks for creating and fetching classes.
3.  On the `DashboardPage.tsx`, display a list of the teacher's classes fetched from the Redux store.
4.  Add a Material-UI `Dialog` or a separate form on the dashboard that allows a teacher to input a class name and dispatch the `createClass` thunk.
5.  The list of classes should update automatically when a new class is created.
```

### Milestone 4: Basic Comic Editor

**Context:** This is the core feature of the application. We will start with a very basic, client-side only comic editor. The goal is to get a functional canvas on the screen where a user can add and manipulate simple elements.

```text
Prompt 4.1: Frontend Comic Editor Canvas Setup

In the frontend project, set up the basic comic editor page and canvas.
1.  Create a new page at `src/pages/EditorPage.tsx`.
2.  Use the HTML `<canvas>` element.
3.  Create a custom hook `useCanvas.ts` that takes a canvas ref and initializes the 2D rendering context.
4.  The `EditorPage` should have a simple layout: a main area for the canvas and a sidebar for tools (which will be empty for now).
5.  The canvas should be initialized to a fixed size (e.g., 800x600) and have a white background.
```

```text
Prompt 4.2: Frontend Element Rendering on Canvas

In the frontend project, add the ability to render simple shapes on the canvas.
1.  In the `EditorPage`, create a state variable using `useState` to hold an array of comic elements (e.g., `elements`). An element could be an object like `{ id, type: 'rect', x, y, width, height, color }`.
2.  In the `useCanvas` hook or a separate drawing utility, create a `draw` function that clears the canvas and iterates through the `elements` array, drawing each one based on its properties.
3.  In the `EditorPage`'s tool sidebar, add a button "Add Rectangle". When clicked, it should add a new rectangle object with default properties to the `elements` state array.
4.  Use a `useEffect` hook to call the `draw` function whenever the `elements` array changes, thus re-rendering the canvas.
```

```text
Prompt 4.3: Frontend Element Manipulation

In the frontend project, add the ability to select and move elements on the canvas.
1.  Add event listeners to the canvas for `mousedown`, `mousemove`, and `mouseup`.
2.  On `mousedown`, check if the click coordinates are inside the bounds of any element in the `elements` array. If so, set that element as the `selectedElement` in a new state variable and record the starting mouse position.
3.  On `mousemove`, if an element is selected, calculate the distance the mouse has moved and update the `x` and `y` properties of the `selectedElement` in the `elements` array. This will trigger a re-render.
4.  On `mouseup`, clear the `selectedElement` state.
5.  This will create a basic drag-and-drop functionality for the shapes on the canvas.
```

### Milestone 5: Saving and Loading Comic Projects (Vertical Slice)

**Context:** This milestone connects the client-side comic editor to the backend, allowing users to save their work and reload it later. This is a critical step in making the editor useful.

```text
Prompt 5.1: Backend Comic Project Model and API Endpoints

In the backend project, define the data model and API for comic projects.
1.  Create a Mongoose schema for `ComicProject` in `src/models/comicProject.model.ts`. The schema should include `title` (String), `student` (ObjectId ref to User), `assignment` (ObjectId ref, optional for now), `pages` (a flexible object/array to store the canvas elements state), and `status` (Enum: 'Draft', 'Submitted').
2.  Create `src/api/comic.routes.ts`.
3.  Implement a protected `POST /api/comics` endpoint. It should create a new `ComicProject` document, associating it with the authenticated user and saving the `pages` data from the request body.
4.  Implement a protected `PUT /api/comics/:id` endpoint that finds a comic by its ID and updates its `pages` data. Ensure only the owner of the comic can update it.
5.  Implement a protected `GET /api/comics/:id` endpoint to retrieve a single comic project's data.
6.  Write integration tests for these endpoints, covering creation, updating, fetching, and authorization rules.
```

```text
Prompt 5.2: Frontend Comic Project Service and Redux Slice

In the frontend project, create the necessary service and state management for comic projects.
1.  Create `src/services/comic.service.ts`. Implement functions to `createComic`, `updateComic`, and `getComicById`, which call the corresponding backend endpoints and include the auth token.
2.  Create `src/store/comic.slice.ts`. The state should manage the current comic project, including its `id`, `title`, `pages` (the elements array), and `status`.
3.  Create async thunks for saving and loading a comic project. The `saveComic` thunk should decide whether to call `createComic` or `updateComic` based on whether the current project already has an ID.
```

```text
Prompt 5.3: Frontend Editor Save/Load Integration

In the frontend project, integrate the save/load functionality into the comic editor.
1.  In `EditorPage.tsx`, add a "Save" button to the toolbar.
2.  When the "Save" button is clicked, it should dispatch the `saveComic` thunk, passing the current array of canvas elements as the `pages` data.
3.  Update the routing so that navigating to `/editor/:comicId` loads the editor for a specific comic.
4.  In `EditorPage.tsx`, use a `useEffect` hook that checks for a `comicId` in the URL parameters. If present, it should dispatch a thunk to fetch the comic data from the backend and populate the local `elements` state, which will then render it to the canvas.
```

### Milestone 6: Student Onboarding (Vertical Slice)

**Context:** This milestone implements the workflow for students to join a teacher's class using a join code, a core requirement for classroom use.

```text
Prompt 6.1: Backend Student Join Endpoint and Tests

In the backend project, create the endpoint for a student to join a class.
1.  In `src/api/class.routes.ts`, implement a public `POST /api/classes/join` endpoint.
2.  The request body should contain a `joinCode` and the student's `name`.
3.  The controller logic should find the class associated with the `joinCode`. If not found, return a 404 error.
4.  If the class is found, create a new `User` with `userType: 'Student'`. Generate a simple, unique username and a temporary password/PIN for the student.
5.  Add the new student's `ObjectId` to the `students` array in the `Class` document.
6.  Return the newly created student's credentials (username/PIN) in the response so they can log in.
7.  Write integration tests for this workflow, covering valid and invalid join codes.
```

```text
Prompt 6.2: Backend Teacher Roster View Endpoint and Tests

In the backend project, create an endpoint for teachers to view their students.
1.  In `src/api/class.routes.ts`, implement a protected teacher-only `GET /api/classes/:id/students` endpoint.
2.  The controller should find the class by its ID, verify the requester is the teacher of that class, and then return the list of students by populating the `students` array with their names and IDs.
3.  Write integration tests to ensure only the correct teacher can view the roster.
```

```text
Prompt 6.3: Frontend Student Join and Teacher Roster UI

In the frontend project, build the UI for student joining and roster viewing.
1.  Create a `JoinClassPage.tsx`. The page should have a simple form for the `joinCode` and student's `name`. On submission, it should call the new service function for the join endpoint. On success, display the returned credentials clearly to the user.
2.  In the teacher's `DashboardPage.tsx`, make each class in the list a link to a new `ClassDetailPage.tsx` (`/class/:id`).
3.  In `ClassDetailPage.tsx`, fetch and display the list of students in that class using the new roster endpoint.
```

### Milestone 7: Assignment Creation and Submission (Vertical Slice)

**Context:** This milestone builds the core educational loop: a teacher creates an assignment, and a student can start a comic project for that assignment and submit it.

```text
Prompt 7.1: Backend Assignment Model and Endpoints

In the backend project, create the model and API for assignments.
1.  Create `src/models/assignment.model.ts` with fields for `title`, `description`, `dueDate`, `class` (ObjectId ref), and `submissions` (Array of ObjectId refs to ComicProject).
2.  Create `src/api/assignment.routes.ts`.
3.  Implement a protected teacher-only `POST /api/assignments` endpoint to create a new assignment associated with a class.
4.  Implement a protected `GET /api/assignments?classId=:classId` endpoint to list all assignments for a given class.
5.  Write integration tests for creating and listing assignments.
```

```text
Prompt 7.2: Frontend Assignment UI for Teachers and Students

In the frontend project, create the UI for managing and viewing assignments.
1.  In the teacher's `ClassDetailPage.tsx`, add a button and form to create a new assignment (capturing title, description, etc.). On submission, it should call a new service function to create the assignment.
2.  Below the roster, display a list of all assignments for that class.
3.  Create a `StudentDashboardPage.tsx`. When a student logs in, they should see a list of their classes. Clicking a class should take them to a `StudentClassDetailPage.tsx` where they can see a list of assignments.
```

```text
Prompt 7.3: Backend and Frontend Submission Flow

Connect comic projects to assignments to enable submission.
1.  In the backend, modify the `POST /api/comics` endpoint to accept an optional `assignmentId`.
2.  Create a new protected `POST /api/comics/:id/submit` endpoint. This should change the `ComicProject` status to 'Submitted' and add its ID to the corresponding `Assignment's` `submissions` array.
3.  In the frontend's `StudentClassDetailPage.tsx`, each assignment in the list should have a "Start" or "Continue" button. This button should navigate the student to the `EditorPage`, creating a new comic project linked to the assignment if one doesn't exist, or loading their draft if it does.
4.  In the `EditorPage`, add a "Submit Assignment" button (visible only for assignment-linked comics). This button will call the `/api/comics/:id/submit` endpoint and should lock the comic from further editing upon success.
```

### Milestone 8: Grading and Feedback (Vertical Slice)

**Context:** This milestone closes the loop by allowing teachers to view submitted work, grade it, and provide feedback.

```text
Prompt 8.1: Backend Grading Model and API

In the backend project, add grading capabilities.
1.  Update the `ComicProject` model to include `grade` (Object, e.g., `{ score: Number, comments: String }`).
2.  Update the `status` enum to include 'Graded'.
3.  Create a protected teacher-only `POST /api/comics/:id/grade` endpoint. The controller should find the comic, verify the teacher has permission to grade it, add the grade and feedback from the request body, and update the status to 'Graded'.
4.  Write integration tests for the grading endpoint.
```

```text
Prompt 8.2: Frontend Grading and Feedback UI

In the frontend project, build the interface for grading.
1.  In the teacher's `ClassDetailPage.tsx`, clicking on an assignment should lead to an `AssignmentSubmissionsPage.tsx`.
2.  This page should list all student submissions for that assignment. Clicking a submission navigates to a `GradingPage.tsx`.
3.  The `GradingPage.tsx` should display the student's comic in a read-only canvas view on one side, and a grading form (for score and comments) on the other.
4.  Submitting the grading form should call the grading API endpoint.
5.  In the student's view, a graded assignment should display the score and comments received from the teacher.
```

---
*This completes the core MVP blueprint. Subsequent phases would build on this foundation to add features like theme-based character libraries, advanced editor tools, PayPal integration, and administrative dashboards as outlined in the full specification.*