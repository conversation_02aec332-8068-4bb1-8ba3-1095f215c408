import express, { Request, Response } from 'express';
import User from '../models/user.model';
import jwt, { SignOptions, Secret } from 'jsonwebtoken';
import dotenv from 'dotenv';
dotenv.config();

const router = express.Router();

// Explicitly type req.body
interface RegisterRequestBody {
  email: string;
  password: string;
  userType: 'Teacher' | 'Student' | 'Admin';
  name: {
    first: string;
    last: string;
  };
}

// POST /api/auth/register
router.post('/register', async (req: Request<{}, {}, RegisterRequestBody>, res: Response) => {
  try {
    const { email, password, userType, name } = req.body;
    if (!email || !password || !userType || !name || !name.first || !name.last) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    const existing = await User.findOne({ email });
    if (existing) {
      return res.status(409).json({ error: 'User with this email already exists' });
    }
    const user = new User({ email, password, userType, name, accountStatus: 'Active' });
    await user.save();
    const { password: _, ...userWithoutPassword } = user.toObject();
    res.status(201).json(userWithoutPassword);
  } catch (err) {
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/auth/login
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;
    if (!email || !password) {
      return res.status(400).json({ error: 'Missing email or password' });
    }
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({ error: 'Invalid email or password' });
    }
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(401).json({ error: 'Invalid email or password' });
    }
    const jwtSecret = process.env.JWT_SECRET as Secret;
    const jwtOptions: SignOptions = {
      expiresIn: (process.env.JWT_EXPIRES_IN as SignOptions['expiresIn']) || '1h'
    };
    const token = jwt.sign(
      { id: user._id, userType: user.userType },
      jwtSecret,
      jwtOptions
    );
    res.status(200).json({ token });
  } catch (err) {
    res.status(500).json({ error: 'Server error' });
  }
});

export default router;
