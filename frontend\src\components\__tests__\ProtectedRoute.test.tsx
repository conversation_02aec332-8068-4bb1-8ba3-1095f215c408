import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import ProtectedRoute from '../ProtectedRoute';
import authReducer from '../../store/auth.slice';

// Mock components for testing
const MockProtectedComponent = () => <div>Protected Content</div>;
const MockLoginPage = () => <div>Login Page</div>;

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: null,
        token: null,
        status: 'idle',
        error: null,
        ...initialState,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState);
  return {
    ...render(
      <Provider store={store}>
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<MockLoginPage />} />
            <Route path="/protected" element={component} />
          </Routes>
        </BrowserRouter>
      </Provider>
    ),
    store,
  };
};

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('ProtectedRoute', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set initial location to /protected
    window.history.pushState({}, '', '/protected');
  });

  it('should render children when user is authenticated', () => {
    renderWithProviders(
      <ProtectedRoute>
        <MockProtectedComponent />
      </ProtectedRoute>,
      {
        token: 'valid-token',
        user: {
          _id: '123',
          email: '<EMAIL>',
          userType: 'Teacher',
          name: { first: 'John', last: 'Doe' },
          accountStatus: 'Active',
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01'
        }
      }
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('should redirect to login when user is not authenticated', () => {
    renderWithProviders(
      <ProtectedRoute>
        <MockProtectedComponent />
      </ProtectedRoute>,
      {
        token: null,
        user: null
      }
    );

    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    expect(mockNavigate).toHaveBeenCalledWith('/login', { 
      state: { from: '/protected' } 
    });
  });

  it('should redirect to login when token exists but user is null', () => {
    renderWithProviders(
      <ProtectedRoute>
        <MockProtectedComponent />
      </ProtectedRoute>,
      {
        token: 'some-token',
        user: null
      }
    );

    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    expect(mockNavigate).toHaveBeenCalledWith('/login', { 
      state: { from: '/protected' } 
    });
  });

  it('should show loading spinner when auth status is loading', () => {
    renderWithProviders(
      <ProtectedRoute>
        <MockProtectedComponent />
      </ProtectedRoute>,
      {
        status: 'loading',
        token: null,
        user: null
      }
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });

  it('should allow access with custom authentication check', () => {
    const customAuthCheck = (isAuthenticated: boolean, user: any) => {
      return isAuthenticated && user?.userType === 'Teacher';
    };

    renderWithProviders(
      <ProtectedRoute authCheck={customAuthCheck}>
        <MockProtectedComponent />
      </ProtectedRoute>,
      {
        token: 'valid-token',
        user: {
          _id: '123',
          email: '<EMAIL>',
          userType: 'Teacher',
          name: { first: 'John', last: 'Doe' },
          accountStatus: 'Active',
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01'
        }
      }
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('should deny access with custom authentication check when condition fails', () => {
    const customAuthCheck = (isAuthenticated: boolean, user: any) => {
      return isAuthenticated && user?.userType === 'Admin';
    };

    renderWithProviders(
      <ProtectedRoute authCheck={customAuthCheck}>
        <MockProtectedComponent />
      </ProtectedRoute>,
      {
        token: 'valid-token',
        user: {
          _id: '123',
          email: '<EMAIL>',
          userType: 'Teacher',
          name: { first: 'John', last: 'Doe' },
          accountStatus: 'Active',
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01'
        }
      }
    );

    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    expect(mockNavigate).toHaveBeenCalledWith('/login', { 
      state: { from: '/protected' } 
    });
  });

  it('should redirect to custom path when specified', () => {
    renderWithProviders(
      <ProtectedRoute redirectTo="/custom-login">
        <MockProtectedComponent />
      </ProtectedRoute>,
      {
        token: null,
        user: null
      }
    );

    expect(mockNavigate).toHaveBeenCalledWith('/custom-login', { 
      state: { from: '/protected' } 
    });
  });

  it('should handle role-based access control', () => {
    const teacherOnlyCheck = (isAuthenticated: boolean, user: any) => {
      return isAuthenticated && user?.userType === 'Teacher';
    };

    // Test with Teacher user
    const { rerender } = renderWithProviders(
      <ProtectedRoute authCheck={teacherOnlyCheck}>
        <MockProtectedComponent />
      </ProtectedRoute>,
      {
        token: 'valid-token',
        user: {
          _id: '123',
          email: '<EMAIL>',
          userType: 'Teacher',
          name: { first: 'John', last: 'Doe' },
          accountStatus: 'Active',
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01'
        }
      }
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('should preserve location state for redirect after login', () => {
    // Set a specific location with state
    window.history.pushState({ someData: 'test' }, '', '/protected?param=value');

    renderWithProviders(
      <ProtectedRoute>
        <MockProtectedComponent />
      </ProtectedRoute>,
      {
        token: null,
        user: null
      }
    );

    expect(mockNavigate).toHaveBeenCalledWith('/login', { 
      state: { from: '/protected?param=value' } 
    });
  });
});
