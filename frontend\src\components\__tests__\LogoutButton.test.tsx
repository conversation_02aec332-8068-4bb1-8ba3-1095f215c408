import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { BrowserRouter } from 'react-router-dom';
import LogoutButton from '../LogoutButton';
import authReducer from '../../store/auth.slice';

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: {
          _id: '123',
          email: '<EMAIL>',
          userType: 'Teacher',
          name: { first: '<PERSON>', last: 'Do<PERSON>' },
          accountStatus: 'Active',
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01'
        },
        token: 'valid-token',
        status: 'succeeded',
        error: null,
        ...initialState,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState);
  return {
    ...render(
      <Provider store={store}>
        <BrowserRouter>
          {component}
        </BrowserRouter>
      </Provider>
    ),
    store,
  };
};

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('LogoutButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render logout button', () => {
    renderWithProviders(<LogoutButton />);
    
    expect(screen.getByRole('button', { name: /logout/i })).toBeInTheDocument();
  });

  it('should dispatch logout action when clicked', async () => {
    const user = userEvent.setup();
    const { store } = renderWithProviders(<LogoutButton />);
    
    const logoutButton = screen.getByRole('button', { name: /logout/i });
    await user.click(logoutButton);
    
    const state = store.getState();
    expect(state.auth.user).toBeNull();
    expect(state.auth.token).toBeNull();
    expect(state.auth.status).toBe('idle');
  });

  it('should redirect to login page after logout', async () => {
    const user = userEvent.setup();
    renderWithProviders(<LogoutButton />);
    
    const logoutButton = screen.getByRole('button', { name: /logout/i });
    await user.click(logoutButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });

  it('should render as menu item when variant is menuItem', () => {
    renderWithProviders(<LogoutButton variant="menuItem" />);
    
    expect(screen.getByRole('menuitem', { name: /logout/i })).toBeInTheDocument();
  });

  it('should render with custom text', () => {
    renderWithProviders(<LogoutButton>Sign Out</LogoutButton>);
    
    expect(screen.getByRole('button', { name: /sign out/i })).toBeInTheDocument();
  });

  it('should show confirmation dialog when confirmLogout is true', async () => {
    const user = userEvent.setup();
    renderWithProviders(<LogoutButton confirmLogout />);
    
    const logoutButton = screen.getByRole('button', { name: /logout/i });
    await user.click(logoutButton);
    
    expect(screen.getByText(/are you sure you want to logout/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /logout/i })).toBeInTheDocument();
  });

  it('should cancel logout when cancel is clicked in confirmation dialog', async () => {
    const user = userEvent.setup();
    const { store } = renderWithProviders(<LogoutButton confirmLogout />);
    
    const logoutButton = screen.getByRole('button', { name: /logout/i });
    await user.click(logoutButton);
    
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);
    
    expect(screen.queryByText(/are you sure you want to logout/i)).not.toBeInTheDocument();
    
    const state = store.getState();
    expect(state.auth.user).not.toBeNull();
    expect(state.auth.token).not.toBeNull();
  });

  it('should proceed with logout when confirmed in dialog', async () => {
    const user = userEvent.setup();
    const { store } = renderWithProviders(<LogoutButton confirmLogout />);
    
    const logoutButton = screen.getByRole('button', { name: /logout/i });
    await user.click(logoutButton);
    
    const confirmButton = screen.getAllByRole('button', { name: /logout/i })[1]; // Second logout button in dialog
    await user.click(confirmButton);
    
    const state = store.getState();
    expect(state.auth.user).toBeNull();
    expect(state.auth.token).toBeNull();
    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });
});
