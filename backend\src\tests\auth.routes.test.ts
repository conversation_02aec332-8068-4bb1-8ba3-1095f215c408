import express, { Request, Response } from 'express';
import bodyParser from 'body-parser';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import request from 'supertest';
import User from '../models/user.model';
import authRouter from '../api/auth.routes';

const app = express();
app.use(bodyParser.json());
app.use('/api/auth', authRouter);

let mongoServer: MongoMemoryServer;

beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  await mongoose.connect(mongoServer.getUri(), {});
});

afterAll(async () => {
  await mongoose.disconnect();
  if (mongoServer) await mongoServer.stop();
});

afterEach(async () => {
  await User.deleteMany({});
});

describe('Auth Registration Endpoint', () => {
  it('should register a new user successfully', async () => {
    const res = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'securepass',
        userType: 'Teacher',
        name: { first: 'Alice', last: 'Smith' }
      });
    expect(res.status).toBe(201);
    expect(res.body.email).toBe('<EMAIL>');
    expect(res.body.userType).toBe('Teacher');
    expect(res.body).not.toHaveProperty('password');
  });

  it('should not allow duplicate email registration', async () => {
    await User.create({
      email: '<EMAIL>',
      password: 'securepass',
      userType: 'Teacher',
      name: { first: 'Bob', last: 'Jones' },
      accountStatus: 'Active'
    });
    const res = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'anotherpass',
        userType: 'Teacher',
        name: { first: 'Bob', last: 'Jones' }
      });
    expect(res.status).toBe(409);
    expect(res.body.error).toMatch(/already exists/i);
  });

  it('should return 400 for missing required fields', async () => {
    const res = await request(app)
      .post('/api/auth/register')
      .send({ email: '', password: '', userType: '', name: {} });
    expect(res.status).toBe(400);
    expect(res.body.error).toBeDefined();
  });
});

describe('Auth Login Endpoint', () => {
  beforeEach(async () => {
    await User.create({
      email: '<EMAIL>',
      password: 'securepass',
      userType: 'Teacher',
      name: { first: 'Carol', last: 'White' },
      accountStatus: 'Active'
    });
  });

  it('should login successfully and return a JWT', async () => {
    const res = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'securepass' });
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('token');
    expect(typeof res.body.token).toBe('string');
  });

  it('should return 401 for incorrect email', async () => {
    const res = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'securepass' });
    expect(res.status).toBe(401);
    expect(res.body.error).toMatch(/invalid/i);
  });

  it('should return 401 for incorrect password', async () => {
    const res = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'wrongpass' });
    expect(res.status).toBe(401);
    expect(res.body.error).toMatch(/invalid/i);
  });
});
